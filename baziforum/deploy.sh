#!/bin/bash

# 八字论坛 Cloudflare Pages 部署脚本
# BaziForum Cloudflare Pages Deployment Script

echo "🚀 开始部署八字论坛到 Cloudflare Pages..."
echo "🚀 Starting BaziForum deployment to Cloudflare Pages..."

# 检查 Node.js 版本
echo "📋 检查环境..."
node_version=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$node_version" -lt 18 ]; then
    echo "❌ 错误: 需要 Node.js 18+ 版本"
    echo "❌ Error: Node.js 18+ required"
    exit 1
fi

# 安装依赖
echo "📦 安装依赖..."
npm install

# 构建项目
echo "🔨 构建项目..."
npm run build

if [ $? -eq 0 ]; then
    echo "✅ 构建成功！"
    echo "✅ Build successful!"
    
    echo ""
    echo "📁 构建文件位于 'out' 目录"
    echo "📁 Build files are in 'out' directory"
    
    echo ""
    echo "🌐 部署选项："
    echo "🌐 Deployment options:"
    echo ""
    echo "1️⃣  Web 界面部署 (推荐):"
    echo "   - 访问: https://dash.cloudflare.com/"
    echo "   - 登录账号: <EMAIL>"
    echo "   - 创建 Pages 项目"
    echo "   - 上传 'out' 目录中的所有文件"
    echo ""
    echo "2️⃣  Wrangler CLI 部署:"
    echo "   - 运行: npm run deploy"
    echo "   - 需要 Node.js 20+ 和 Wrangler CLI"
    echo ""
    echo "3️⃣  Git 集成部署:"
    echo "   - 推送代码到 GitHub"
    echo "   - 在 Cloudflare Pages 连接仓库"
    echo ""
    
    # 显示构建统计
    echo "📊 构建统计:"
    echo "   - 总页面数: $(find out -name "*.html" | wc -l)"
    echo "   - 静态资源: $(find out -type f | wc -l) 个文件"
    echo "   - 构建大小: $(du -sh out | cut -f1)"
    
else
    echo "❌ 构建失败！"
    echo "❌ Build failed!"
    echo "请检查错误信息并修复后重试"
    echo "Please check error messages and retry after fixing"
    exit 1
fi

echo ""
echo "🎉 准备完成！选择上述任一方式进行部署。"
echo "🎉 Ready! Choose any of the above methods to deploy."
