{"name": "baziforum", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "export": "next build && next export", "deploy": "npm run build && npx wrangler pages deploy out --project-name=baziforum", "deploy:preview": "npm run build && npx wrangler pages deploy out --project-name=baziforum --env=preview"}, "dependencies": {"@headlessui/react": "^2.2.4", "clsx": "^2.1.1", "lucide-react": "^0.512.0", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5", "wrangler": "^4.19.1"}}