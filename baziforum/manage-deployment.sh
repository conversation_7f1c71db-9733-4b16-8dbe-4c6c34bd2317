#!/bin/bash

# 八字论坛 Cloudflare Pages 管理脚本
# BaziForum Cloudflare Pages Management Script

# 设置 Node.js 环境
setup_node() {
    export NVM_DIR="$HOME/.nvm"
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
    nvm use 20 > /dev/null 2>&1
}

# 显示帮助信息
show_help() {
    echo "🚀 八字论坛 Cloudflare Pages 管理工具"
    echo "🚀 BaziForum Cloudflare Pages Management Tool"
    echo ""
    echo "用法 / Usage:"
    echo "  ./manage-deployment.sh [命令]"
    echo ""
    echo "可用命令 / Available commands:"
    echo "  deploy          部署到生产环境 / Deploy to production"
    echo "  deploy-preview  部署到预览环境 / Deploy to preview"
    echo "  status          查看项目状态 / Check project status"
    echo "  logs            查看部署日志 / View deployment logs"
    echo "  domains         查看域名信息 / View domain info"
    echo "  rollback        回滚部署 / Rollback deployment"
    echo "  help            显示此帮助 / Show this help"
    echo ""
    echo "当前部署地址 / Current deployment:"
    echo "  🌐 https://916c1f43.baziforum.pages.dev"
    echo "  🌐 https://baziforum.pages.dev"
}

# 部署到生产环境
deploy_production() {
    echo "🚀 开始部署到生产环境..."
    setup_node
    npm run build
    if [ $? -eq 0 ]; then
        npx wrangler pages deploy out --project-name=baziforum
    else
        echo "❌ 构建失败，部署中止"
        exit 1
    fi
}

# 部署到预览环境
deploy_preview() {
    echo "🔍 开始部署到预览环境..."
    setup_node
    npm run build
    if [ $? -eq 0 ]; then
        npx wrangler pages deploy out --project-name=baziforum --env=preview
    else
        echo "❌ 构建失败，部署中止"
        exit 1
    fi
}

# 查看项目状态
check_status() {
    echo "📊 查看项目状态..."
    setup_node
    echo ""
    echo "项目列表:"
    npx wrangler pages project list
    echo ""
    echo "部署历史:"
    npx wrangler pages deployment list --project-name=baziforum
}

# 查看部署日志
view_logs() {
    echo "📋 查看最近的部署..."
    setup_node
    npx wrangler pages deployment list --project-name=baziforum --limit=5
}

# 查看域名信息
view_domains() {
    echo "🌐 查看域名配置..."
    setup_node
    npx wrangler pages project get baziforum
}

# 回滚部署
rollback_deployment() {
    echo "🔙 可用的部署版本:"
    setup_node
    npx wrangler pages deployment list --project-name=baziforum --limit=10
    echo ""
    echo "要回滚到特定版本，请使用:"
    echo "npx wrangler pages deployment rollback <deployment-id> --project-name=baziforum"
}

# 主逻辑
case "$1" in
    "deploy")
        deploy_production
        ;;
    "deploy-preview")
        deploy_preview
        ;;
    "status")
        check_status
        ;;
    "logs")
        view_logs
        ;;
    "domains")
        view_domains
        ;;
    "rollback")
        rollback_deployment
        ;;
    "help"|"--help"|"-h"|"")
        show_help
        ;;
    *)
        echo "❌ 未知命令: $1"
        echo "使用 './manage-deployment.sh help' 查看可用命令"
        exit 1
        ;;
esac
