# 🎯 八字论坛 (BaziForum)

<div align="center">

![八字论坛](https://img.shields.io/badge/八字论坛-新闻自由讨论平台-blue?style=for-the-badge)
![Next.js](https://img.shields.io/badge/Next.js-15-black?style=for-the-badge&logo=next.js)
![TypeScript](https://img.shields.io/badge/TypeScript-5-blue?style=for-the-badge&logo=typescript)
![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-3-38B2AC?style=for-the-badge&logo=tailwind-css)
![Cloudflare](https://img.shields.io/badge/Cloudflare-Pages-orange?style=for-the-badge&logo=cloudflare)

**新闻自由 · 舆论监督 · 开放讨论**

[🌐 在线访问](https://baziforum.pages.dev) · [📖 文档](./docs/) · [🚀 快速开始](#快速开始) · [💡 功能特性](#功能特性)

</div>

---

## 📋 项目简介

八字论坛是一个专注于**新闻自由**和**舆论监督**的现代化讨论平台。采用传统八字概念设计了8个核心讨论板块，为用户提供一个开放、自由、安全的信息交流空间。

### 🎨 设计理念

> *"不仅关乎社会，也关乎你我。当信息无法自由传播，我们所知道的就不一定是真相；当公众无法表达质疑，错误就可能一再发生。这些权利，是你我的日常保障。"*

## ⚡ 技术栈

<table>
<tr>
<td>

**前端技术**
- 🚀 Next.js 15 (App Router)
- 📘 TypeScript 5
- 🎨 Tailwind CSS 3
- 🎯 Lucide React Icons
- 📱 响应式设计

</td>
<td>

**部署 & 性能**
- ☁️ Cloudflare Pages
- 🌍 全球 CDN 加速
- 🔒 自动 HTTPS/SSL
- ⚡ 静态站点生成 (SSG)
- 📊 实时分析

</td>
</tr>
</table>

## 🎯 功能特性

### 🏛️ 八大核心板块

<div align="center">

| 板块 | 名称 | 描述 | 图标 |
|:---:|:---:|:---|:---:|
| **新** | 最新事件 | 收集当日发生的重大新闻，持续关注入口 | 🔥 |
| **闻** | 重大档案 | 主要大件档案，档案记录，档案分析 | 📰 |
| **自** | 自由观点 | 自由发表观点，自由讨论，自由分析 | 💭 |
| **由** | 事实核查 | 透明的调查过程，信息来源的验证 | 🔍 |
| **舆** | 媒体监督 | 监督媒体报道，媒体监督，媒体分析 | 👁️ |
| **论** | 公共辩论 | 公共议题的理性讨论和多元观点交流 | ⚖️ |
| **监** | 调查报告 | 深度调查报告，独立调查，调查分析 | 📋 |
| **督** | 社区互动 | 社区建设，用户互动，社区管理 | 🤝 |

</div>

### 🌟 核心功能

- ✅ **8个主题讨论板块** - 覆盖新闻自由的各个方面
- ✅ **响应式设计** - 完美适配桌面端、平板端、移动端
- ✅ **现代化UI** - 简洁美观的用户界面
- ✅ **快速加载** - 静态站点生成，全球CDN加速
- ✅ **智能导航** - 可收起的侧边导航栏
- ✅ **示例内容** - 25个示例帖子和完整的内容结构

### 📱 响应式设计亮点

<table>
<tr>
<td align="center">

**🖥️ 桌面端**<br>
1920×1080 一屏展示<br>
4列网格布局<br>
完整侧边导航

</td>
<td align="center">

**📱 移动端**<br>
触摸友好设计<br>
2列网格布局<br>
滑出式菜单

</td>
<td align="center">

**📊 平板端**<br>
平衡的视觉效果<br>
3列网格布局<br>
混合交互模式

</td>
</tr>
</table>

## 🚀 快速开始

### 📋 环境要求

- **Node.js**: 18.0.0 或更高版本
- **包管理器**: npm、yarn 或 pnpm
- **浏览器**: 现代浏览器 (Chrome, Firefox, Safari, Edge)

### ⚙️ 安装与运行

```bash
# 1. 克隆项目
git clone https://github.com/your-username/baziforum.git
cd baziforum

# 2. 安装依赖
npm install

# 3. 启动开发服务器
npm run dev

# 4. 打开浏览器访问
# http://localhost:3000
```

### 🔨 构建与部署

```bash
# 构建生产版本
npm run build

# 部署到 Cloudflare Pages
npm run deploy

# 预览部署
npm run deploy:preview

# 使用管理脚本
./manage-deployment.sh deploy
```

## 📁 项目结构

```
baziforum/
├── 📁 src/
│   ├── 📁 app/                    # Next.js App Router
│   │   ├── 📁 [slug]/            # 动态路由 - 板块页面
│   │   │   ├── page.tsx          # 板块主页
│   │   │   └── new/page.tsx      # 发帖页面
│   │   ├── 📁 post/[id]/         # 帖子详情页
│   │   ├── 📁 about/             # 关于页面
│   │   ├── 📁 join/              # 加入我们
│   │   ├── layout.tsx            # 根布局
│   │   ├── page.tsx              # 首页
│   │   └── globals.css           # 全局样式
│   ├── 📁 components/            # React 组件
│   │   ├── DynamicIcon.tsx       # 动态图标组件
│   │   ├── SideNavigation.tsx    # 侧边导航
│   │   ├── PostCard.tsx          # 帖子卡片
│   │   ├── MobileOptimized.tsx   # 移动端优化
│   │   └── ResponsiveGrid.tsx    # 响应式网格
│   ├── 📁 data/                  # 数据文件
│   │   ├── sections.ts           # 板块配置
│   │   ├── posts.ts              # 示例帖子
│   │   └── comments.ts           # 示例评论
│   ├── 📁 types/                 # TypeScript 类型
│   └── 📁 lib/                   # 工具函数
├── 📁 public/                    # 静态资源
├── 📁 docs/                      # 项目文档
├── 📄 wrangler.toml              # Cloudflare 配置
├── 📄 next.config.ts             # Next.js 配置
├── 📄 tailwind.config.ts         # Tailwind 配置
└── 📄 package.json               # 项目配置
```

## 🎨 设计系统

### 🎯 响应式断点

```css
/* 移动端 */
@media (max-width: 768px) {
  /* 2列布局，紧凑间距 */
}

/* 平板端 */
@media (min-width: 768px) and (max-width: 1024px) {
  /* 3列布局，平衡设计 */
}

/* 桌面端 */
@media (min-width: 1024px) {
  /* 4列布局，完整功能 */
}

/* 大屏幕优化 */
@media (min-width: 1920px) {
  /* 1920×1080 一屏显示优化 */
}
```

### 🎨 设计原则

- **📱 移动优先**: Mobile-first 响应式设计
- **🎯 触摸友好**: 最小44px触摸目标
- **⚡ 性能优先**: 优化加载速度和交互体验
- **🎨 视觉一致**: 统一的设计语言和组件系统

## 🌐 部署信息

### 🚀 生产环境

| 环境 | 地址 | 状态 |
|:---:|:---|:---:|
| **生产环境** | [baziforum.pages.dev](https://baziforum.pages.dev) | 🟢 在线 |
| **最新部署** | [2dbe5b02.baziforum.pages.dev](https://2dbe5b02.baziforum.pages.dev) | 🟢 在线 |

### ⚙️ 部署配置

- **平台**: Cloudflare Pages
- **构建命令**: `npm run build`
- **输出目录**: `out`
- **Node.js 版本**: 20.x
- **部署方式**: 自动部署 + 手动部署

### 📊 性能指标

- ⚡ **首屏加载**: < 2秒
- 🎯 **交互就绪**: < 3秒
- 📊 **Lighthouse 评分**: 90+
- 🌍 **全球CDN**: 150+ 节点

## 🛠️ 开发指南

### 🔧 添加新板块

1. **配置板块信息**
   ```typescript
   // src/data/sections.ts
   {
     key: '新',
     name: '新板块',
     slug: 'new-section',
     description: '板块描述',
     icon: 'IconName'
   }
   ```

2. **添加图标支持**
   ```typescript
   // src/components/DynamicIcon.tsx
   case 'IconName':
     return <IconName {...props} />;
   ```

3. **测试响应式布局**

### 🎨 自定义样式

```css
/* src/app/globals.css */

/* 自定义组件样式 */
.card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-4 md:p-6;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .custom-mobile {
    /* 移动端样式 */
  }
}
```

### 🧩 组件开发规范

```typescript
// 组件模板
interface ComponentProps {
  children: ReactNode;
  className?: string;
}

export default function Component({ children, className = '' }: ComponentProps) {
  return (
    <div className={`component-base ${className}`}>
      {children}
    </div>
  );
}
```

## 📈 性能优化

### ✅ 已实现优化

- 🚀 **静态站点生成 (SSG)** - 预渲染所有页面
- 📦 **代码分割** - 按需加载组件
- 🖼️ **图片优化** - 响应式图片处理
- 🎨 **CSS 优化** - 压缩和去重
- ⚡ **懒加载** - 组件和资源懒加载
- 🌍 **CDN 加速** - Cloudflare 全球网络

### 📊 监控指标

```bash
# 性能测试
npm run build
npm run start

# Lighthouse 测试
npx lighthouse http://localhost:3000 --view
```

## 🌍 浏览器支持

| 浏览器 | 版本支持 | 状态 |
|:---:|:---:|:---:|
| **Chrome** | 最新版本 | ✅ 完全支持 |
| **Firefox** | 最新版本 | ✅ 完全支持 |
| **Safari** | 最新版本 | ✅ 完全支持 |
| **Edge** | 最新版本 | ✅ 完全支持 |
| **移动端浏览器** | iOS Safari, Chrome Mobile | ✅ 完全支持 |

## 🤝 贡献指南

### 🔄 开发流程

1. **Fork 项目** 到您的 GitHub 账户
2. **创建功能分支** `git checkout -b feature/amazing-feature`
3. **提交更改** `git commit -m 'Add amazing feature'`
4. **推送分支** `git push origin feature/amazing-feature`
5. **创建 Pull Request**

### 📝 代码规范

- ✅ **ESLint**: 代码质量检查
- ✅ **TypeScript**: 严格类型检查
- ✅ **Prettier**: 代码格式化
- ✅ **命名规范**:
  - 组件: `PascalCase`
  - 文件: `kebab-case`
  - 变量: `camelCase`

### 🧪 测试规范

```bash
# 运行测试
npm run test

# 类型检查
npm run type-check

# 代码检查
npm run lint
```

## 📄 许可证

本项目采用 **MIT 许可证** - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

| 联系方式 | 信息 |
|:---:|:---|
| 🌐 **项目主页** | [baziforum.pages.dev](https://baziforum.pages.dev) |
| 🐛 **问题反馈** | [GitHub Issues](https://github.com/your-username/baziforum/issues) |
| 📧 **邮箱联系** | <EMAIL> |
| 📚 **项目文档** | [查看文档](./docs/) |

## 🙏 致谢

感谢所有为**新闻自由**和**舆论监督**事业做出贡献的人们。

特别感谢：
- 🚀 **Next.js 团队** - 提供优秀的React框架
- 🎨 **Tailwind CSS** - 现代化的CSS框架
- ☁️ **Cloudflare** - 可靠的部署平台
- 🎯 **Lucide** - 精美的图标库

---

**🎯 八字论坛** - *致力于推动新闻自由与舆论监督*

*© 2024 八字论坛. 保留所有权利.*
