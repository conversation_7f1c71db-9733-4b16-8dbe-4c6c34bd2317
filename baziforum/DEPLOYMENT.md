# 🚀 Cloudflare Pages 部署指南

## 📋 项目概述

**八字论坛 (BaziForum)** - 新闻自由讨论平台
- **技术栈**: Next.js 15 + TypeScript + Tailwind CSS
- **部署平台**: Cloudflare Pages
- **账号**: <EMAIL>

## 🔧 部署配置

项目已完成 Cloudflare Pages 部署配置：

### ✅ 配置文件
- `next.config.ts` - 静态导出配置
- `wrangler.toml` - Cloudflare 配置
- `package.json` - 部署脚本

### ✅ 构建设置
```json
{
  "output": "export",
  "trailingSlash": true,
  "images": { "unoptimized": true }
}
```

## 🚀 部署方法

### 方法一：Web 界面部署（推荐）

#### 步骤 1：构建项目
```bash
npm run build
```

#### 步骤 2：登录 Cloudflare
1. 访问 https://dash.cloudflare.com/
2. 使用账号 `<EMAIL>` 登录

#### 步骤 3：创建 Pages 项目
1. 点击左侧菜单 **"Pages"**
2. 点击 **"Create a project"**
3. 选择 **"Upload assets"** 选项卡

#### 步骤 4：上传文件
1. 将 `out` 目录中的所有文件打包为 ZIP
2. 或直接拖拽 `out` 目录内容到上传区域

#### 步骤 5：配置项目
- **项目名称**: `baziforum`
- **生产分支**: main
- **自定义域名**: 可选配置

### 方法二：Git 集成部署

#### 步骤 1：推送到 GitHub
```bash
git add .
git commit -m "Deploy to Cloudflare Pages"
git push origin main
```

#### 步骤 2：连接 Git 仓库
1. 在 Cloudflare Pages 中选择 **"Connect to Git"**
2. 选择您的 GitHub 仓库
3. 配置构建设置：
   - **构建命令**: `npm run build`
   - **构建输出目录**: `out`
   - **Node.js 版本**: `20.x`

### 方法三：Wrangler CLI 部署

#### 前提条件
- Node.js >= 20.0.0
- 安装 Wrangler CLI

```bash
# 安装 Wrangler
npm install -g wrangler

# 登录 Cloudflare
wrangler login

# 部署
npm run deploy
```

## 🔧 构建脚本

项目包含以下部署相关脚本：

```json
{
  "scripts": {
    "build": "next build",
    "export": "next build && next export",
    "deploy": "npm run build && npx wrangler pages deploy out"
  }
}
```

## 🌐 域名配置

### 默认域名
部署后将获得类似以下的默认域名：
- `baziforum.pages.dev`
- `baziforum-xxx.pages.dev`

### 自定义域名
可在 Cloudflare Pages 设置中配置自定义域名：
1. 进入项目设置
2. 点击 **"Custom domains"**
3. 添加您的域名
4. 配置 DNS 记录

## 📊 性能优化

### 已启用优化
- ✅ 静态站点生成 (SSG)
- ✅ 图片优化禁用（适配 Cloudflare）
- ✅ 代码分割和懒加载
- ✅ CSS 优化和压缩
- ✅ 响应式设计

### Cloudflare 功能
- 🌍 全球 CDN 加速
- 🔒 自动 HTTPS
- 📈 实时分析
- 🛡️ DDoS 防护
- ⚡ 边缘计算

## 🔍 部署验证

部署完成后，验证以下功能：

### ✅ 页面检查
- [ ] 首页加载正常
- [ ] 各板块页面可访问
- [ ] 帖子详情页显示正确
- [ ] 发帖页面功能正常

### ✅ 导航功能
- [ ] 侧边导航栏展开/收起
- [ ] 移动端菜单正常
- [ ] 板块间切换流畅
- [ ] 返回首页功能

### ✅ 响应式设计
- [ ] 桌面端布局正确
- [ ] 平板端适配良好
- [ ] 移动端体验流畅
- [ ] 各屏幕尺寸正常

## 🐛 常见问题

### 构建失败
```bash
# 清理缓存重新构建
rm -rf .next out
npm run build
```

### 路由问题
- 确保 `trailingSlash: true` 配置
- 检查 `out` 目录结构

### 样式问题
- 验证 Tailwind CSS 配置
- 检查静态资源路径

## 📞 支持联系

如遇部署问题，可以：
1. 查看 Cloudflare Pages 部署日志
2. 检查项目构建输出
3. 参考 Cloudflare 官方文档

---

**部署完成后，您的八字论坛将在全球范围内快速访问！** 🌍
