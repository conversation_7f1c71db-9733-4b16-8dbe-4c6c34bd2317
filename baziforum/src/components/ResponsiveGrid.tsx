'use client';

import { ReactNode } from 'react';

interface ResponsiveGridProps {
  children: ReactNode;
  className?: string;
  cols?: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
  };
  gap?: {
    mobile?: string;
    tablet?: string;
    desktop?: string;
  };
}

/**
 * 响应式网格组件
 * 针对不同屏幕尺寸优化布局
 */
export default function ResponsiveGrid({ 
  children, 
  className = '',
  cols = { mobile: 2, tablet: 3, desktop: 4 },
  gap = { mobile: '1rem', tablet: '1.5rem', desktop: '2rem' }
}: ResponsiveGridProps) {
  const gridClasses = [
    // 移动端
    `grid-cols-${cols.mobile || 2}`,
    // 平板端
    `md:grid-cols-${cols.tablet || 3}`,
    // 桌面端
    `lg:grid-cols-${cols.desktop || 4}`,
    // 间距
    'gap-4 md:gap-6 lg:gap-8'
  ].join(' ');

  return (
    <div className={`grid ${gridClasses} ${className}`}>
      {children}
      
      <style jsx>{`
        /* 确保网格项目等高 */
        .grid > * {
          height: 100%;
          display: flex;
          flex-direction: column;
        }
        
        /* 移动端优化 */
        @media (max-width: 768px) {
          .grid {
            gap: ${gap.mobile || '1rem'};
          }
          
          /* 移动端卡片优化 */
          .grid > * {
            min-height: 160px;
          }
        }
        
        /* 平板端优化 */
        @media (min-width: 768px) and (max-width: 1024px) {
          .grid {
            gap: ${gap.tablet || '1.5rem'};
          }
          
          .grid > * {
            min-height: 180px;
          }
        }
        
        /* 桌面端优化 */
        @media (min-width: 1024px) {
          .grid {
            gap: ${gap.desktop || '2rem'};
          }
          
          .grid > * {
            min-height: 200px;
          }
        }
        
        /* 大屏幕优化 - 确保1920*1080能完整显示 */
        @media (min-width: 1920px) {
          .grid {
            max-width: 1600px;
            margin: 0 auto;
          }
          
          .grid > * {
            min-height: 220px;
          }
        }
      `}</style>
    </div>
  );
}
