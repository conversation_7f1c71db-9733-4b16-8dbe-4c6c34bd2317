'use client';

import { useState } from 'react';
import { BaziSection } from '@/types';
import { Save, Eye } from 'lucide-react';

interface NewPostFormProps {
  section: BaziSection;
}

export default function NewPostForm({ section }: NewPostFormProps) {
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [tags, setTags] = useState('');
  const [isPreview, setIsPreview] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!title.trim() || !content.trim()) return;

    setIsSubmitting(true);
    
    // 模拟提交
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    alert('发帖功能暂未开放，这是一个演示版本');
    setIsSubmitting(false);
  };

  const tagList = tags.split(',').map(tag => tag.trim()).filter(tag => tag);

  return (
    <div className="w-full px-4 sm:px-6 lg:px-8 py-8">
      <div className="flex justify-end mb-4">
        <button
          type="button"
          onClick={() => setIsPreview(!isPreview)}
          className="btn-secondary flex items-center space-x-2"
        >
          <Eye className="w-4 h-4" />
          <span>{isPreview ? '编辑' : '预览'}</span>
        </button>
      </div>

      <form onSubmit={handleSubmit}>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          {!isPreview ? (
            <>
              {/* Title Input */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  标题 *
                </label>
                <input
                  type="text"
                  required
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  className="w-full px-3 py-2 text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="请输入帖子标题..."
                  maxLength={100}
                />
                <div className="text-right text-sm text-gray-500 mt-1">
                  {title.length}/100
                </div>
              </div>

              {/* Content Input */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  内容 *
                </label>
                <textarea
                  required
                  value={content}
                  onChange={(e) => setContent(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                  rows={12}
                  placeholder="请输入帖子内容..."
                />
                <div className="text-right text-sm text-gray-500 mt-1">
                  {content.length} 字符
                </div>
              </div>

              {/* Tags Input */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  标签 (可选)
                </label>
                <input
                  type="text"
                  value={tags}
                  onChange={(e) => setTags(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="请输入标签，用逗号分隔，如：学术诚信,高校,举报"
                />
                {tagList.length > 0 && (
                  <div className="flex flex-wrap gap-2 mt-2">
                    {tagList.map((tag, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 text-sm bg-blue-100 text-blue-700 rounded"
                      >
                        #{tag}
                      </span>
                    ))}
                  </div>
                )}
              </div>
            </>
          ) : (
            /* Preview Mode */
            <div>
              <div className="mb-4">
                <div className="flex items-center space-x-2 mb-2">
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                    section.key === '新' ? 'bg-red-100 text-red-700' :
                    section.key === '闻' ? 'bg-blue-100 text-blue-700' :
                    section.key === '自' ? 'bg-green-100 text-green-700' :
                    section.key === '由' ? 'bg-yellow-100 text-yellow-700' :
                    section.key === '舆' ? 'bg-purple-100 text-purple-700' :
                    section.key === '论' ? 'bg-indigo-100 text-indigo-700' :
                    section.key === '监' ? 'bg-orange-100 text-orange-700' :
                    'bg-gray-100 text-gray-700'
                  }`}>
                    {section.key}
                  </span>
                  <span className="text-sm text-gray-500">刚刚</span>
                </div>
                <h1 className="text-2xl font-bold text-gray-900 mb-2">
                  {title || '标题预览'}
                </h1>
                <div className="text-sm text-gray-500 mb-4">
                  作者：您的用户名
                </div>
              </div>

              <div className="prose max-w-none mb-6">
                <div className="text-gray-700 leading-relaxed whitespace-pre-line">
                  {content || '内容预览'}
                </div>
              </div>

              {tagList.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {tagList.map((tag, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 text-sm bg-gray-100 text-gray-600 rounded-full"
                    >
                      #{tag}
                    </span>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Submit Button */}
          <div className="flex justify-end pt-6 border-t border-gray-200">
            <button
              type="submit"
              disabled={!title.trim() || !content.trim() || isSubmitting}
              className="btn-primary flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Save className="w-4 h-4" />
              <span>{isSubmitting ? '发布中...' : '发布帖子'}</span>
            </button>
          </div>
        </div>
      </form>

      {/* Guidelines */}
      <div className="mt-6 bg-blue-50 rounded-lg p-4">
        <h3 className="font-medium text-blue-900 mb-2">发帖指南</h3>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• 请确保内容与 {section.name} 板块主题相关</li>
          <li>• 保持理性讨论，尊重不同观点</li>
          <li>• 提供可靠的信息来源，避免传播未经证实的消息</li>
          <li>• 使用恰当的标签帮助其他用户找到您的帖子</li>
        </ul>
      </div>
    </div>
  );
}
