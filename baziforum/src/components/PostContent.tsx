'use client';

import { useState } from 'react';
import { Post, Comment } from '@/types';
import { formatDate, formatNumber } from '@/lib/utils';
import { Heart, MessageCircle, Eye } from 'lucide-react';

interface PostContentProps {
  post: Post;
  comments: Comment[];
}

export default function PostContent({ post, comments }: PostContentProps) {
  const [liked, setLiked] = useState(false);
  const [likeCount, setLikeCount] = useState(post.likes);
  const [comment, setComment] = useState('');

  const handleLike = () => {
    setLiked(!liked);
    setLikeCount(prev => liked ? prev - 1 : prev + 1);
  };

  const handleCommentSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (comment.trim()) {
      alert('评论功能暂未开放，这是一个演示版本');
      setComment('');
    }
  };

  return (
    <div className="w-full px-4 sm:px-6 lg:px-8 py-8">
      {/* Post */}
      <article className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        {/* Post Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <span className={`px-3 py-1 text-sm font-medium rounded-full ${
              post.category === '新' ? 'bg-red-100 text-red-700' :
              post.category === '闻' ? 'bg-blue-100 text-blue-700' :
              post.category === '自' ? 'bg-green-100 text-green-700' :
              post.category === '由' ? 'bg-yellow-100 text-yellow-700' :
              post.category === '舆' ? 'bg-purple-100 text-purple-700' :
              post.category === '论' ? 'bg-indigo-100 text-indigo-700' :
              post.category === '监' ? 'bg-orange-100 text-orange-700' :
              'bg-gray-100 text-gray-700'
            }`}>
              {post.category}
            </span>
            {post.isHot && <span className="text-red-500">🔥</span>}
          </div>
          <span className="text-sm text-gray-500">{formatDate(post.createdAt)}</span>
        </div>

        {/* Post Title */}
        <h1 className="text-2xl font-bold text-gray-900 mb-4">{post.title}</h1>

        {/* Post Meta */}
        <div className="flex items-center space-x-6 text-sm text-gray-500 mb-6">
          <span className="font-medium">{post.author}</span>
          <div className="flex items-center space-x-1">
            <Eye className="w-4 h-4" />
            <span>{formatNumber(post.views)} 阅读</span>
          </div>
        </div>

        {/* Post Content */}
        <div className="prose max-w-none mb-6">
          <p className="text-gray-700 leading-relaxed whitespace-pre-line">
            {post.content}
          </p>
        </div>

        {/* Post Tags */}
        {post.tags.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-6">
            {post.tags.map((tag) => (
              <span
                key={tag}
                className="px-3 py-1 text-sm bg-gray-100 text-gray-600 rounded-full"
              >
                #{tag}
              </span>
            ))}
          </div>
        )}

        {/* Post Actions */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-200">
          <div className="flex items-center space-x-6">
            <button
              onClick={handleLike}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                liked 
                  ? 'bg-red-50 text-red-600 hover:bg-red-100' 
                  : 'text-gray-500 hover:bg-gray-50 hover:text-red-600'
              }`}
            >
              <Heart className={`w-5 h-5 ${liked ? 'fill-current' : ''}`} />
              <span>{formatNumber(likeCount)}</span>
            </button>
            
            <div className="flex items-center space-x-2 text-gray-500">
              <MessageCircle className="w-5 h-5" />
              <span>{formatNumber(post.comments)}</span>
            </div>
          </div>
        </div>
      </article>

      {/* Comments Section */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">
          评论 ({comments.length})
        </h2>

        {/* Comment Form */}
        <form onSubmit={handleCommentSubmit} className="mb-6">
          <textarea
            value={comment}
            onChange={(e) => setComment(e.target.value)}
            placeholder="写下你的想法..."
            className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            rows={3}
          />
          <div className="flex justify-end mt-3">
            <button
              type="submit"
              disabled={!comment.trim()}
              className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              发表评论
            </button>
          </div>
        </form>

        {/* Comments List */}
        <div className="space-y-4">
          {comments.length > 0 ? (
            comments.map((comment) => (
              <div key={comment.id} className="border-b border-gray-100 pb-4 last:border-b-0">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium text-gray-900">{comment.author}</span>
                  <span className="text-sm text-gray-500">{formatDate(comment.createdAt)}</span>
                </div>
                <p className="text-gray-700 mb-2">{comment.content}</p>
                <div className="flex items-center space-x-4 text-sm text-gray-500">
                  <button className="hover:text-red-600 transition-colors">
                    <Heart className="w-4 h-4 inline mr-1" />
                    {comment.likes}
                  </button>
                  <button className="hover:text-blue-600 transition-colors">
                    回复
                  </button>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-8 text-gray-500">
              暂无评论，来发表第一条评论吧！
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
