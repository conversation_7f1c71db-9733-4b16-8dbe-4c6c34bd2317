import { 
  Flame, 
  Archive, 
  PenTool, 
  SearchCheck, 
  Eye, 
  MessageSquare, 
  Scale, 
  ClipboardList 
} from 'lucide-react';

const iconMap = {
  Flame,
  Archive,
  PenTool,
  SearchCheck,
  Eye,
  MessageSquare,
  Scale,
  ClipboardList,
};

interface DynamicIconProps {
  name: string;
  className?: string;
}

export default function DynamicIcon({ name, className = "w-6 h-6" }: DynamicIconProps) {
  const IconComponent = iconMap[name as keyof typeof iconMap];
  
  if (!IconComponent) {
    return <div className={className} />;
  }
  
  return <IconComponent className={className} />;
}
