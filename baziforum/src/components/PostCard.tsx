import Link from 'next/link';
import { Post } from '@/types';
import { formatDate, formatNumber } from '@/lib/utils';
import { Heart, MessageCircle, Eye, Pin } from 'lucide-react';

interface PostCardProps {
  post: Post;
}

export default function PostCard({ post }: PostCardProps) {
  return (
    <div className="card hover:shadow-md transition-shadow">
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center space-x-2">
          <span className={`px-2 py-1 text-xs font-medium rounded-full ${
            post.category === '新' ? 'bg-red-100 text-red-700' :
            post.category === '闻' ? 'bg-blue-100 text-blue-700' :
            post.category === '自' ? 'bg-green-100 text-green-700' :
            post.category === '由' ? 'bg-yellow-100 text-yellow-700' :
            post.category === '舆' ? 'bg-purple-100 text-purple-700' :
            post.category === '论' ? 'bg-indigo-100 text-indigo-700' :
            post.category === '监' ? 'bg-orange-100 text-orange-700' :
            'bg-gray-100 text-gray-700'
          }`}>
            {post.category}
          </span>
          {post.isSticky && (
            <Pin className="w-4 h-4 text-orange-500" />
          )}
          {post.isHot && (
            <span className="text-red-500 text-xs">🔥</span>
          )}
        </div>
        <span className="text-xs text-gray-500">{formatDate(post.createdAt)}</span>
      </div>
      
      <Link href={`/post/${post.id}`} className="block group">
        <h3 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors mb-2 line-clamp-2">
          {post.title}
        </h3>
        <p className="text-gray-600 text-sm mb-3 line-clamp-2">
          {post.excerpt}
        </p>
      </Link>
      
      <div className="flex items-center justify-between text-sm text-gray-500">
        <div className="flex items-center space-x-1">
          <span className="font-medium">{post.author}</span>
        </div>
        
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-1">
            <Heart className="w-4 h-4" />
            <span>{formatNumber(post.likes)}</span>
          </div>
          <div className="flex items-center space-x-1">
            <MessageCircle className="w-4 h-4" />
            <span>{formatNumber(post.comments)}</span>
          </div>
          <div className="flex items-center space-x-1">
            <Eye className="w-4 h-4" />
            <span>{formatNumber(post.views)}</span>
          </div>
        </div>
      </div>
      
      {post.tags.length > 0 && (
        <div className="flex flex-wrap gap-1 mt-3">
          {post.tags.slice(0, 3).map((tag) => (
            <span
              key={tag}
              className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded"
            >
              #{tag}
            </span>
          ))}
        </div>
      )}
    </div>
  );
}
