'use client';

import { ReactNode } from 'react';

interface MobileOptimizedProps {
  children: ReactNode;
  className?: string;
}

/**
 * 移动端优化容器组件
 * 提供移动端友好的布局和交互优化
 */
export default function MobileOptimized({ children, className = '' }: MobileOptimizedProps) {
  return (
    <div className={`mobile-optimized ${className}`}>
      {children}
      
      <style jsx>{`
        .mobile-optimized {
          /* 移动端触摸优化 */
          -webkit-tap-highlight-color: transparent;
          -webkit-touch-callout: none;
          -webkit-user-select: none;
          user-select: none;
        }
        
        /* 移动端滚动优化 */
        .mobile-optimized * {
          -webkit-overflow-scrolling: touch;
          scroll-behavior: smooth;
        }
        
        /* 移动端字体优化 */
        @media (max-width: 768px) {
          .mobile-optimized {
            font-size: 16px; /* 防止 iOS Safari 缩放 */
            line-height: 1.5;
          }
          
          /* 移动端按钮优化 */
          .mobile-optimized button,
          .mobile-optimized a {
            min-height: 44px;
            min-width: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          
          /* 移动端输入框优化 */
          .mobile-optimized input,
          .mobile-optimized textarea {
            font-size: 16px; /* 防止 iOS Safari 缩放 */
            border-radius: 8px;
            padding: 12px;
          }
        }
        
        /* 平板端优化 */
        @media (min-width: 768px) and (max-width: 1024px) {
          .mobile-optimized {
            font-size: 15px;
          }
        }
      `}</style>
    </div>
  );
}
