@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), system-ui, -apple-system, sans-serif;
  scroll-behavior: smooth;
}

/* Custom components */
.card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-4 md:p-6 transition-all duration-200;
}

.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-2xl transition-colors shadow-sm hover:shadow-md;
}

.btn-secondary {
  @apply bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-2xl transition-colors shadow-sm hover:shadow-md;
}

.text-gradient {
  @apply bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent;
}

/* 八字特殊样式 - 优化尺寸 */
.bazi-char {
  @apply text-3xl md:text-4xl lg:text-5xl font-bold text-gray-800 hover:text-blue-600 transition-colors cursor-pointer;
  font-family: "SimSun", "宋体", serif;
}

/* 文本截断 */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* 响应式设计优化 */
@media (max-width: 768px) {
  .bazi-char {
    @apply text-2xl;
  }

  .card {
    @apply p-3;
  }

  /* 移动端优化 */
  body {
    font-size: 14px;
  }
}

/* 平板端优化 */
@media (min-width: 768px) and (max-width: 1024px) {
  .bazi-char {
    @apply text-3xl;
  }
}

/* 大屏幕优化 - 确保1920*1080能一屏展示 */
@media (min-width: 1920px) {
  .container {
    max-width: 1600px;
  }

  /* 首页优化 - 压缩垂直空间 */
  section {
    padding-top: 2rem !important;
    padding-bottom: 2rem !important;
  }

  /* 标题优化 */
  h2 {
    margin-bottom: 1.5rem !important;
  }

  h3 {
    margin-bottom: 2rem !important;
  }
}

/* 1920*1080 专门优化 */
@media (min-width: 1920px) and (max-height: 1080px) {
  /* 进一步压缩垂直空间 */
  .min-h-screen {
    min-height: 100vh;
  }

  /* Hero section 特别优化 */
  section:first-of-type {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }

  /* Footer 优化 */
  footer {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .btn-primary,
  .btn-secondary {
    @apply py-3 px-6 text-base;
    min-height: 44px; /* iOS 推荐的最小触摸目标 */
  }

  .card {
    @apply p-4;
  }
}
