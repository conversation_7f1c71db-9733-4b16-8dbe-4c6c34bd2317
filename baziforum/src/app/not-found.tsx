import Link from 'next/link';
import { Home } from 'lucide-react';

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="max-w-md mx-auto text-center">
        <div className="text-6xl font-bold text-gray-300 mb-4">404</div>
        <h1 className="text-2xl font-bold text-gray-900 mb-2">页面未找到</h1>
        <p className="text-gray-600 mb-8">
          抱歉，您访问的页面不存在或已被移除。
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link href="/" className="btn-primary flex items-center justify-center space-x-2">
            <Home className="w-4 h-4" />
            <span>返回首页</span>
          </Link>
          <Link href="/forum" className="btn-secondary flex items-center justify-center space-x-2">
            <span>进入论坛</span>
          </Link>
        </div>
      </div>
    </div>
  );
}
