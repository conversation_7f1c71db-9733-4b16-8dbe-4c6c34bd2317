'use client';

import { useState } from 'react';
import Link from 'next/link';
import { ArrowLeft, Code, PenTool, Users, ExternalLink } from 'lucide-react';
import { JoinFormData } from '@/types';

export default function JoinPage() {
  const [formData, setFormData] = useState<JoinFormData>({
    name: '',
    email: '',
    role: 'developer',
    message: '',
    skills: [],
    experience: ''
  });

  const [submitted, setSubmitted] = useState(false);

  const roles = [
    {
      key: 'developer' as const,
      title: '前端开发',
      description: 'React/Next.js方向，负责平台功能开发',
      icon: Code,
      skills: ['React', 'Next.js', 'TypeScript', 'Tailwind CSS']
    },
    {
      key: 'content' as const,
      title: '内容策划',
      description: '公共议题关注者，负责内容策划与写作',
      icon: PenTool,
      skills: ['新闻写作', '议题分析', '内容策划', '社会观察']
    },
    {
      key: 'volunteer' as const,
      title: '社群志愿者',
      description: '协助社区运营、用户支持等工作',
      icon: Users,
      skills: ['社区运营', '用户支持', '活动策划', '沟通协调']
    }
  ];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // 这里应该提交到后端
    console.log('Form submitted:', formData);
    setSubmitted(true);
  };

  const handleSkillToggle = (skill: string) => {
    setFormData(prev => ({
      ...prev,
      skills: prev.skills?.includes(skill)
        ? prev.skills.filter(s => s !== skill)
        : [...(prev.skills || []), skill]
    }));
  };

  if (submitted) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md mx-auto bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
          <div className="text-green-500 text-6xl mb-4">✓</div>
          <h2 className="text-2xl font-bold text-gray-900 mb-4">申请已提交</h2>
          <p className="text-gray-600 mb-6">
            感谢您的申请！我们会在3个工作日内通过邮件与您联系。
          </p>
          <div className="space-y-3">
            <Link href="/" className="btn-primary w-full">
              返回首页
            </Link>
            <Link href="/about" className="btn-secondary w-full">
              了解更多
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center h-16">
            <Link href="/" className="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors">
              <ArrowLeft className="w-5 h-5" />
              <span>返回首页</span>
            </Link>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">加入我们</h1>
          <p className="text-xl text-gray-600">
            一起推动新闻自由与舆论监督的发展
          </p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8 mb-12">
          {roles.map((role) => {
            const Icon = role.icon;
            return (
              <div
                key={role.key}
                className={`card cursor-pointer transition-all ${
                  formData.role === role.key 
                    ? 'ring-2 ring-blue-500 bg-blue-50' 
                    : 'hover:shadow-md'
                }`}
                onClick={() => setFormData(prev => ({ ...prev, role: role.key }))}
              >
                <div className="text-center">
                  <Icon className="w-12 h-12 mx-auto mb-4 text-blue-600" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{role.title}</h3>
                  <p className="text-gray-600 text-sm mb-4">{role.description}</p>
                  <div className="flex flex-wrap gap-1 justify-center">
                    {role.skills.map((skill) => (
                      <span
                        key={skill}
                        className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">申请表单</h2>
          
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  姓名 *
                </label>
                <input
                  type="text"
                  required
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="请输入您的姓名"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  邮箱 *
                </label>
                <input
                  type="email"
                  required
                  value={formData.email}
                  onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="请输入您的邮箱"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                相关技能
              </label>
              <div className="flex flex-wrap gap-2">
                {roles.find(r => r.key === formData.role)?.skills.map((skill) => (
                  <button
                    key={skill}
                    type="button"
                    onClick={() => handleSkillToggle(skill)}
                    className={`px-3 py-1 text-sm rounded-full transition-colors ${
                      formData.skills?.includes(skill)
                        ? 'bg-blue-100 text-blue-700 border border-blue-300'
                        : 'bg-gray-100 text-gray-600 border border-gray-300 hover:bg-gray-200'
                    }`}
                  >
                    {skill}
                  </button>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                相关经验
              </label>
              <textarea
                value={formData.experience}
                onChange={(e) => setFormData(prev => ({ ...prev, experience: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={3}
                placeholder="请简述您的相关经验（可选）"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                申请理由 *
              </label>
              <textarea
                required
                value={formData.message}
                onChange={(e) => setFormData(prev => ({ ...prev, message: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={4}
                placeholder="请告诉我们您为什么想要加入我们，以及您希望为项目贡献什么..."
              />
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <button
                type="submit"
                className="btn-primary flex-1"
              >
                提交申请
              </button>
              
              <a
                href="https://forms.gle/example"
                target="_blank"
                rel="noopener noreferrer"
                className="btn-secondary flex-1 flex items-center justify-center space-x-2"
              >
                <span>Google Forms</span>
                <ExternalLink className="w-4 h-4" />
              </a>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
