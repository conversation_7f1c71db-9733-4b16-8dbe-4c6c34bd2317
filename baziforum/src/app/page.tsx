import Link from 'next/link';
import { baziSections } from '@/data/sections';
import DynamicIcon from '@/components/DynamicIcon';
import MobileOptimized from '@/components/MobileOptimized';
import ResponsiveGrid from '@/components/ResponsiveGrid';

export default function Home() {
  return (
    <MobileOptimized className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header - 优化高度 */}
      <header className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-12 md:h-14">
            <div className="flex items-center space-x-2">
              <h1 className="text-lg md:text-xl font-bold text-gradient">八字论坛</h1>
              <span className="text-xs md:text-sm text-gray-500 hidden sm:inline">新闻自由 舆论监督</span>
            </div>
            <nav className="flex space-x-3 md:space-x-6">
              <Link href="/about" className="text-gray-600 hover:text-blue-600 transition-colors text-sm md:text-base">
                项目理念
              </Link>
              <Link href="/join" className="btn-primary text-sm md:text-base py-1.5 px-3 md:py-2 md:px-4">
                加入我们
              </Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section - 压缩高度 */}
      <section className="py-8 md:py-12 px-4 text-center">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4 md:mb-6">
            新闻自由 舆论监督
          </h2>
          <p className="text-base md:text-lg text-gray-600 mb-6 md:mb-8 max-w-2xl mx-auto">
            不仅关乎社会，也关乎你我。当信息无法自由传播，我们所知道的就不一定是真相。
          </p>
          <div className="flex flex-col sm:flex-row gap-3 md:gap-4 justify-center">
            <Link href="/forum" className="btn-primary text-base md:text-lg px-6 md:px-8 py-2.5 md:py-3">
              进入论坛
            </Link>
            <Link href="/about" className="btn-secondary text-base md:text-lg px-6 md:px-8 py-2.5 md:py-3">
              了解更多
            </Link>
          </div>
        </div>
      </section>

      {/* Bazi Grid - 优化间距和尺寸 */}
      <section className="py-8 md:py-12 px-4">
        <div className="max-w-6xl mx-auto">
          <h3 className="text-2xl md:text-3xl font-bold text-center mb-8 md:mb-10 text-gray-900">
            八字论坛
          </h3>
          <ResponsiveGrid
            cols={{ mobile: 2, tablet: 3, desktop: 4 }}
            gap={{ mobile: '1rem', tablet: '1.5rem', desktop: '2rem' }}
          >
            {baziSections.map((section) => (
              <Link
                key={section.key}
                href={`/${section.slug}`}
                className="group h-full"
              >
                <div className="card hover:shadow-lg transition-all duration-300 group-hover:scale-105 text-center h-full flex flex-col justify-between">
                  <div className="flex-1 flex flex-col justify-center">
                    <div className="flex justify-center mb-3 md:mb-4">
                      <DynamicIcon
                        name={section.icon}
                        className="w-8 h-8 md:w-10 md:h-10 text-gray-600 group-hover:text-blue-600 transition-colors"
                      />
                    </div>
                    <div className="bazi-char mb-2">{section.key}</div>
                    <h4 className="font-semibold text-gray-900 mb-2 text-sm md:text-base">{section.name}</h4>
                  </div>
                  <p className="text-xs md:text-sm text-gray-600 line-clamp-2 mt-auto">{section.description}</p>
                </div>
              </Link>
            ))}
          </ResponsiveGrid>
        </div>
      </section>

      {/* Footer - 压缩高度 */}
      <footer className="bg-gray-900 text-white py-8 md:py-10 px-4">
        <div className="max-w-4xl mx-auto text-center">
          <blockquote className="text-sm md:text-base mb-4 md:mb-6 italic leading-relaxed">
            &ldquo;新闻自由，舆论监督——不仅关乎社会，也关乎你我。<br className="hidden md:block" />
            当信息无法自由传播，我们所知道的就不一定是真相；<br className="hidden md:block" />
            当公众无法表达质疑，错误就可能一再发生。<br className="hidden md:block" />
            这些权利，是你我的日常保障。&rdquo;
          </blockquote>
          <div className="flex flex-col sm:flex-row gap-3 md:gap-4 justify-center mb-6 md:mb-8">
            <Link href="/join" className="btn-primary text-sm md:text-base py-2 px-4 md:py-2.5 md:px-6">
              加入我们
            </Link>
            <Link href="/freemedia" className="btn-secondary bg-gray-700 hover:bg-gray-600 text-white text-sm md:text-base py-2 px-4 md:py-2.5 md:px-6">
              了解更多
            </Link>
          </div>
          <p className="text-gray-400 text-xs md:text-sm">
            © 2024 八字论坛. 致力于推动新闻自由与舆论监督.
          </p>
        </div>
      </footer>
    </MobileOptimized>
  );
}
