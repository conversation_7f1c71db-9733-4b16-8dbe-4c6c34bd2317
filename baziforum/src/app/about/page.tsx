import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center h-16">
            <Link href="/" className="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors">
              <ArrowLeft className="w-5 h-5" />
              <span>返回首页</span>
            </Link>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          {/* Hero Section */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              新闻自由 舆论监督
            </h1>
            <p className="text-xl text-gray-600">
              不是宏大叙事，而是日常保障
            </p>
          </div>

          {/* Main Content */}
          <div className="prose max-w-none">
            <blockquote className="text-lg text-gray-700 border-l-4 border-blue-500 pl-6 mb-8 italic">
              &ldquo;在我们的生活中，每一次知情、发声、参与的机会，背后都依赖一个基础：信息公开与表达自由。&rdquo;
            </blockquote>

            <div className="grid md:grid-cols-2 gap-8 mb-12">
              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-4">为什么重要？</h2>
                <ul className="space-y-3 text-gray-700">
                  <li className="flex items-start">
                    <span className="text-blue-500 mr-2">•</span>
                    当疫情初起，你能知道真实的感染数据，是新闻自由在发挥作用
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-500 mr-2">•</span>
                    当学生举报校园权力滥用被重视，是舆论监督在起作用
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-500 mr-2">•</span>
                    当食品安全、房屋质量、环境污染被曝光，是记者、网民、调查者在守护社会底线
                  </li>
                </ul>
              </div>

              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-4">关乎你我</h2>
                <ul className="space-y-3 text-gray-700">
                  <li className="flex items-start">
                    <span className="text-green-500 mr-2">✓</span>
                    你能否获得真实信息
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-2">✓</span>
                    你能否提出质疑
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-2">✓</span>
                    你是否被听见，被理解，被保护
                  </li>
                </ul>
              </div>
            </div>

            <div className="bg-blue-50 rounded-lg p-6 mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">我们的使命</h2>
              <p className="text-gray-700 leading-relaxed">
                若缺乏这些制度性的&ldquo;耳目&rdquo;，<strong>信息就会失真，权力就会失控，个体将无处申冤、无从自保。</strong>
                我们建设这个平台，正是希望唤起更多人对这些&ldquo;看不见但重要&rdquo;的机制的理解，并亲自参与到这场时代的对话中来。
              </p>
            </div>

            <div className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">八字论坛的理念</h2>
              <p className="text-gray-700 leading-relaxed mb-4">
                我们以&ldquo;新、闻、自、由、舆、论、监、督&rdquo;八个字构建论坛结构，每个字代表一个重要的讨论领域：
              </p>
              
              <div className="grid md:grid-cols-2 gap-4 text-sm">
                <div className="space-y-2">
                  <div><strong>新</strong> - 最新事件：关注当下热点</div>
                  <div><strong>闻</strong> - 重大事件档案：构建公共记忆</div>
                  <div><strong>自</strong> - 个人观点发表：自由表达空间</div>
                  <div><strong>由</strong> - 假新闻溯源：培养辨别能力</div>
                </div>
                <div className="space-y-2">
                  <div><strong>舆</strong> - 媒体观察：分析舆论机制</div>
                  <div><strong>论</strong> - 话题辩论场：理性讨论平台</div>
                  <div><strong>监</strong> - 举报与公审：社区自治机制</div>
                  <div><strong>督</strong> - 社区进展公告：透明化建设</div>
                </div>
              </div>
            </div>

            <div className="bg-gray-50 rounded-lg p-6 mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">项目现状</h2>
              <p className="text-gray-700 leading-relaxed mb-4">
                这是一个Demo版本，旨在展示平台理念、结构和部分交互功能。我们希望通过这个原型：
              </p>
              <ul className="space-y-2 text-gray-700">
                <li>• 以结构化内容呈现新闻/舆论生态问题</li>
                <li>• 提供互动框架鼓励发言、辩论与监督意识</li>
                <li>• 吸引关注社会议题的学生、程序员、内容创作者加入开发</li>
                <li>• 后期逐步拓展成拥有后端与真实数据处理能力的社区平台</li>
              </ul>
            </div>

            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">加入我们</h2>
              <p className="text-gray-700 mb-6">
                如果你认同我们的理念，愿意为推动新闻自由和舆论监督贡献力量，欢迎加入我们的团队。
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/join" className="btn-primary">
                  立即加入
                </Link>
                <Link href="/" className="btn-secondary">
                  进入论坛
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
