import Link from 'next/link';
import { ArrowLeft, Newspaper, Users, Shield, Globe } from 'lucide-react';

export default function FreeMediaPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center h-16">
            <Link href="/" className="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors">
              <ArrowLeft className="w-5 h-5" />
              <span>返回首页</span>
            </Link>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          {/* Hero Section */}
          <div className="text-center mb-12">
            <div className="flex justify-center mb-6">
              <div className="p-4 bg-blue-100 rounded-full">
                <Newspaper className="w-12 h-12 text-blue-600" />
              </div>
            </div>
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              新闻自由
            </h1>
            <p className="text-xl text-gray-600">
              保障公众知情权，使真实不被扭曲
            </p>
          </div>

          {/* Main Content */}
          <div className="prose max-w-none">
            <div className="bg-blue-50 rounded-lg p-6 mb-8">
              <h2 className="text-2xl font-bold text-blue-900 mb-4">什么是新闻自由？</h2>
              <p className="text-blue-800 leading-relaxed">
                新闻自由是指媒体和记者有权自由地收集、报道和传播信息，不受政府、企业或其他权力机构的不当干预。
                它是民主社会的基石，确保公众能够获得准确、及时、多元的信息。
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-6 mb-12">
              <div className="text-center p-6 bg-gray-50 rounded-lg">
                <Users className="w-8 h-8 mx-auto mb-4 text-green-600" />
                <h3 className="font-semibold text-gray-900 mb-2">知情权</h3>
                <p className="text-sm text-gray-600">
                  公众有权了解影响他们生活的事件和决策
                </p>
              </div>
              
              <div className="text-center p-6 bg-gray-50 rounded-lg">
                <Shield className="w-8 h-8 mx-auto mb-4 text-blue-600" />
                <h3 className="font-semibold text-gray-900 mb-2">监督权力</h3>
                <p className="text-sm text-gray-600">
                  媒体作为第四权力，监督政府和企业行为
                </p>
              </div>
              
              <div className="text-center p-6 bg-gray-50 rounded-lg">
                <Globe className="w-8 h-8 mx-auto mb-4 text-purple-600" />
                <h3 className="font-semibold text-gray-900 mb-2">多元声音</h3>
                <p className="text-sm text-gray-600">
                  保障不同观点和声音都能被听见
                </p>
              </div>
            </div>

            <div className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">为什么新闻自由如此重要？</h2>
              
              <div className="space-y-6">
                <div className="border-l-4 border-blue-500 pl-6">
                  <h3 className="font-semibold text-gray-900 mb-2">防止信息垄断</h3>
                  <p className="text-gray-700">
                    当信息被少数人控制时，真相可能被扭曲或隐瞒。新闻自由确保信息来源的多样性，
                    让公众能够从不同角度了解事件真相。
                  </p>
                </div>

                <div className="border-l-4 border-green-500 pl-6">
                  <h3 className="font-semibold text-gray-900 mb-2">促进民主参与</h3>
                  <p className="text-gray-700">
                    只有在充分了解情况的基础上，公民才能做出明智的政治选择。
                    新闻自由为民主决策提供了必要的信息基础。
                  </p>
                </div>

                <div className="border-l-4 border-purple-500 pl-6">
                  <h3 className="font-semibold text-gray-900 mb-2">保护弱势群体</h3>
                  <p className="text-gray-700">
                    媒体报道能够揭露不公正现象，为弱势群体发声，
                    推动社会关注和解决问题。
                  </p>
                </div>

                <div className="border-l-4 border-orange-500 pl-6">
                  <h3 className="font-semibold text-gray-900 mb-2">推动社会进步</h3>
                  <p className="text-gray-700">
                    通过揭露问题、引发讨论，新闻报道能够推动政策改革和社会进步，
                    促进更加公正和透明的社会环境。
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-yellow-50 rounded-lg p-6 mb-8">
              <h2 className="text-2xl font-bold text-yellow-900 mb-4">现实中的挑战</h2>
              <ul className="space-y-3 text-yellow-800">
                <li className="flex items-start">
                  <span className="text-yellow-600 mr-2">•</span>
                  政府审查和媒体管制
                </li>
                <li className="flex items-start">
                  <span className="text-yellow-600 mr-2">•</span>
                  商业利益对新闻独立性的影响
                </li>
                <li className="flex items-start">
                  <span className="text-yellow-600 mr-2">•</span>
                  虚假信息和谣言的传播
                </li>
                <li className="flex items-start">
                  <span className="text-yellow-600 mr-2">•</span>
                  记者人身安全受到威胁
                </li>
                <li className="flex items-start">
                  <span className="text-yellow-600 mr-2">•</span>
                  技术平台的算法控制
                </li>
              </ul>
            </div>

            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">我们能做什么？</h2>
              <p className="text-gray-700 mb-6">
                作为公民，我们每个人都可以为维护新闻自由贡献力量：
              </p>
              
              <div className="grid md:grid-cols-2 gap-4 text-left mb-8">
                <div className="space-y-2">
                  <div>✓ 支持独立媒体和调查报道</div>
                  <div>✓ 培养媒体素养，学会辨别信息</div>
                  <div>✓ 关注和分享有价值的新闻内容</div>
                </div>
                <div className="space-y-2">
                  <div>✓ 参与公共讨论，表达理性观点</div>
                  <div>✓ 监督和举报虚假信息</div>
                  <div>✓ 保护记者和媒体工作者的权益</div>
                </div>
              </div>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/publicwatch" className="btn-primary">
                  了解舆论监督
                </Link>
                <Link href="/join" className="btn-secondary">
                  加入我们
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
