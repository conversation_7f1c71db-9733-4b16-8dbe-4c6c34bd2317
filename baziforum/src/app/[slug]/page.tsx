import Link from 'next/link';
import { notFound } from 'next/navigation';
import { baziSections } from '@/data/sections';
import { samplePosts } from '@/data/posts';
import DynamicIcon from '@/components/DynamicIcon';
import SectionContent from '@/components/SectionContent';
import SideNavigation from '@/components/SideNavigation';
import { ArrowLeft, Plus } from 'lucide-react';

export async function generateStaticParams() {
  return baziSections.map((section) => ({
    slug: section.slug,
  }));
}

interface SectionPageProps {
  params: Promise<{
    slug: string;
  }>;
}

export default async function SectionPage({ params }: SectionPageProps) {
  const { slug } = await params;

  const section = baziSections.find(s => s.slug === slug);
  if (!section) {
    notFound();
  }

  // 过滤当前板块的帖子
  const sectionPosts = samplePosts.filter(post => post.category === section.key);

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Side Navigation */}
      <SideNavigation currentSlug={section.slug} />

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <header className="bg-white border-b border-gray-200 sticky top-0 z-40">
          <div className="w-full px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center space-x-4 md:ml-0 ml-16">
                <Link href="/" className="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors">
                  <ArrowLeft className="w-5 h-5" />
                  <span className="hidden sm:inline">返回首页</span>
                </Link>
                <div className="h-6 w-px bg-gray-300" />
                <div className="flex items-center space-x-3">
                  <DynamicIcon
                    name={section.icon}
                    className="w-8 h-8 text-gray-600"
                  />
                  <div>
                    <h1 className="text-xl font-bold text-gray-900">{section.name}</h1>
                    <p className="text-sm text-gray-600 hidden sm:block">{section.description}</p>
                  </div>
                </div>
              </div>

              <Link href={`/${section.slug}/new`} className="btn-primary flex items-center space-x-2">
                <Plus className="w-4 h-4" />
                <span className="hidden sm:inline">发帖</span>
                <span className="sm:hidden">+</span>
              </Link>
            </div>
          </div>
        </header>

        <SectionContent section={section} posts={sectionPosts} />
      </div>
    </div>
  );
}
