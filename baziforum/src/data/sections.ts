import { BaziSection } from '@/types';

export const baziSections: BaziSection[] = [
  {
    key: '新',
    slug: 'latest',
    name: '最新事件',
    description: '浏览当前社会热点内容，作为热榜入口',
    color: 'bg-red-500',
    icon: 'Flame'
  },
  {
    key: '闻',
    slug: 'archives',
    name: '重大事件档案',
    description: '汇总关键事件、转折记录，构成"公共记忆"',
    color: 'bg-blue-500',
    icon: 'Archive'
  },
  {
    key: '自',
    slug: 'opinions',
    name: '个人观点发表',
    description: '自由表达区，含日志、原创内容等',
    color: 'bg-green-500',
    icon: 'PenTool'
  },
  {
    key: '由',
    slug: 'factcheck',
    name: '假新闻溯源',
    description: '提供谣言分析、信息溯源内容，引导辟谣意识',
    color: 'bg-yellow-500',
    icon: 'SearchCheck'
  },
  {
    key: '舆',
    slug: 'mediawatch',
    name: '媒体观察',
    description: '分析媒体偏向、舆论塑造机制，促思辨',
    color: 'bg-purple-500',
    icon: 'Eye'
  },
  {
    key: '论',
    slug: 'debates',
    name: '话题辩论场',
    description: '开设辩题，设立正反立场，引导参与讨论',
    color: 'bg-indigo-500',
    icon: 'MessageSquare'
  },
  {
    key: '监',
    slug: 'reports',
    name: '举报与公审',
    description: '用户举报展示，管理员与用户共同评价',
    color: 'bg-orange-500',
    icon: 'Scale'
  },
  {
    key: '督',
    slug: 'community',
    name: '社区进展公告',
    description: '开发日志、任务列表、人员招募、制度建设',
    color: 'bg-gray-500',
    icon: 'ClipboardList'
  }
];
