export interface Post {
  id: string;
  title: string;
  content: string;
  excerpt: string;
  author: string;
  authorAvatar?: string;
  category: BaziCategory;
  createdAt: string;
  updatedAt: string;
  likes: number;
  comments: number;
  views: number;
  tags: string[];
  isSticky?: boolean;
  isHot?: boolean;
}

export interface Comment {
  id: string;
  postId: string;
  author: string;
  authorAvatar?: string;
  content: string;
  createdAt: string;
  likes: number;
  replies?: Comment[];
}

export type BaziCategory = '新' | '闻' | '自' | '由' | '舆' | '论' | '监' | '督';

export interface BaziSection {
  key: BaziCategory;
  slug: string;
  name: string;
  description: string;
  color: string;
  icon: string;
}

export type TabType = 'hot' | 'latest' | 'mine';

export interface User {
  id: string;
  username: string;
  avatar?: string;
  email: string;
  joinedAt: string;
  role: 'visitor' | 'user' | 'admin';
}

export interface JoinFormData {
  name: string;
  email: string;
  role: 'developer' | 'content' | 'volunteer';
  message: string;
  skills?: string[];
  experience?: string;
}
