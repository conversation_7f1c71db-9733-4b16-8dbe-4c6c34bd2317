
# 🧾 产品开发需求文档（PRD）

## 📌 项目名称

**“八字论坛”Demo**：面向大学生的公共议题内容型社交平台

---

## 1. 🎯 项目概述

### 项目定位

构建一个以“新闻自由、舆论监督”为价值核心的轻型论坛原型，用以展示平台理念、结构和部分交互，并作为招募技术合作者和内容参与者的起点。

### 项目目标

* 以结构化内容呈现新闻/舆论生态问题
* 提供互动框架鼓励发言、辩论与监督意识
* 吸引关注社会议题的学生、程序员、内容创作者加入开发
* 后期逐步拓展成拥有后端与真实数据处理能力的社区平台

---

## 2. 🧭 核心理念与愿景

> **“新闻自由，舆论监督，不是抽象口号，而是每个人的生活保障。”**

* **新闻自由**：保障公众知情权，使真实不被扭曲
* **舆论监督**：制衡权力，保护弱者，倒逼改进
* 论坛以“八字”为板块（新、闻、自、由、舆、论、监、督），形成一种具有文化意象的结构化讨论空间

---

## 3. 👥 用户角色定义

| 角色    | 权限           | 功能示例          |
| ----- | ------------ | ------------- |
| 访客    | 浏览内容、查看结构    | 查看板块、阅读帖子     |
| 注册用户  | 模拟发帖、评论、收藏等  | 提交观点、参与辩论、发建议 |
| 项目管理员 | 管理内容与展示、公告发布 | 发布运营进度、精选内容   |

---

## 4. 🗂 功能模块一览（Demo级）

### 4.1 模块架构总览

* **首页**：八字入口卡片 + 项目介绍 + 加入我们
* **八大板块（新、闻、自、由、舆、论、监、督）**

  * 每板块含三个页签：`热门 | 最新 | 我的`
  * 每页显示内容卡片（帖标题 + 摘要 + 点赞数等）
* **内容页**：帖详情 + 评论列表
* **互动机制**：点赞、评论、收藏（伪功能）
* **发帖页**：标题 + 正文 + 板块选择（模拟提交）
* **加入我们页**：项目愿景 + 表单链接（Google Form/Notion）
* **底部栏**：理念说明 + 联系方式 + 社群链接

---

### 4.2 板块功能设计（按八字）

| 板块           | 核心功能说明                 |
| ------------ | ---------------------- |
| **新**：最新事件   | 浏览当前社会热点内容，作为热榜入口      |
| **闻**：重大事件档案 | 汇总关键事件、转折记录，构成“公共记忆”   |
| **自**：个人观点发表 | 自由表达区，含日志、原创内容等        |
| **由**：假新闻溯源  | 提供谣言分析、信息溯源内容，引导辟谣意识   |
| **舆**：媒体观察   | 分析媒体偏向、舆论塑造机制，促思辨      |
| **论**：话题辩论场  | 开设辩题，设立正反立场，引导参与讨论     |
| **监**：举报与公审  | 用户举报展示，管理员与用户共同评价（伪功能） |
| **督**：社区进展公告 | 开发日志、任务列表、人员招募、制度建设    |

---

## 5. 🧱 页面设计与结构说明

### 5.1 首页

* 八个“字”组成主视觉界面（现代汉字+书法字体）
* 下方按钮跳转：

  * 加入我们
  * 了解项目理念
  * 论坛入口按钮

### 5.2 板块页（任一字进入）

* 页签导航：`最新 | 热门 | 我的`
* 内容列表卡片（含标题、摘要、点赞数、评论数）
* 发帖按钮（模拟发帖）

### 5.3 内容详情页

* 帖子标题、正文
* 评论列表（伪数据或可留言）
* 点赞、收藏功能（本地状态切换）

### 5.4 加入我们页

* 项目介绍
* 表单链接按钮（Google Form）
* 加入角色选项：开发者 / 内容编辑 / 社群志愿者

### 5.5 底部栏

```text
新闻自由，舆论监督——不仅关乎社会，也关乎你我。
当信息无法自由传播，我们所知道的就不一定是真相；
当公众无法表达质疑，错误就可能一再发生。
这些权利，是你我的日常保障。
```

---

## 6. 📊 数据结构（模拟）

* 内容数据：JSON文件或JS静态数据数组
* 用户行为：本地状态储存（收藏、点赞）
* 发帖与评论：只需实现前端提交与显示，数据不存储

---

## 7. 🛠 技术选型建议

| 模块    | 技术推荐                       | 说明             |
| ----- | -------------------------- | -------------- |
| 前端框架  | React + Next.js + Tailwind | 组件灵活，样式易控，部署方便 |
| UI组件库 | Headless UI / shadcn/ui    | 高度可自定义、现代感     |
| 状态管理  | 本地状态或 Zustand              | 仅限于模拟交互状态      |
| 部署平台  | Vercel / Netlify           | 免费且对前端友好       |
| 数据模拟  | JSON / Markdown 内容静态加载     | 避免数据库配置复杂性     |

---

## 8. ⏳ 开发阶段与任务拆分（Demo）

| 阶段  | 内容                  | 工期估计  |
| --- | ------------------- | ----- |
| 阶段一 | 首页 + 八字结构页（静态）      | 2-3 天 |
| 阶段二 | 每板块子页（卡片列表）+ 内容页    | 3-5 天 |
| 阶段三 | 发帖交互（模拟）+ 用户行为状态    | 2 天   |
| 阶段四 | 加入我们页 + 表单接入 + 底部信息 | 1-2 天 |
| 阶段五 | 美化 + 响应式适配 + 部署     | 2 天   |

---

## 9. 📣 团队招募方向

### 核心合作者角色

* 前端开发（React/Next.js方向）
* UI/UX设计（有内容共情力的设计者）
* 内容策划与写作者（公共议题关注者）
* 策略顾问/产品联合发起人（具备议题思维）

### 加入方式

* 页面中置入“加入我们”表单（Google Form）
* 项目邮件 / Telegram 群 / GitHub组织链接展示

---

## 10. 🔗 附录：配套资源建议

* \[新闻传播学推荐阅读书单]
* \[开源舆情分析平台参考]
* \[GitHub 项目规划模板]
* \[内容发布行为守则草案（初稿）]

---

## ✅ 最后说明

这个PRD是一个**内容驱动 + 公共价值导向**的产品规划文档，目标是展示平台理念、鼓励参与，并吸引真正具备议题思维的开发者、内容人加入构建。



为了让浏览者在进入这个项目时，不只是“围观结构”，而是真正理解**为什么我们需要新闻自由和舆论监督**，我们需要在底部（或项目介绍页）加入一段**思想引导型的说明性文字**。

---

## ✅ 简洁版说明（适合底部footer区域）

> **新闻自由，舆论监督——不仅关乎社会，也关乎你我。**
>
> 当信息无法自由传播，我们所知道的就不一定是真相；当公众无法表达质疑，错误就可能一再发生。
>
> **新闻自由**让我们拥有接触事实的权利，**舆论监督**让权力面对公众的目光。
>
> 这些权利，构成了我们生活中的安全感、公平感和尊严。它们不是遥远的政治概念，而是你我的日常保障。

---

## ✅ 延展版说明（适合“项目愿景”页）

> ### 新闻自由 舆论监督：不是宏大叙事，而是日常保障
>
> 在我们的生活中，每一次知情、发声、参与的机会，背后都依赖一个基础：**信息公开与表达自由**。
>
> * 当疫情初起，你能知道真实的感染数据，是新闻自由在发挥作用；
> * 当学生举报校园权力滥用被重视，是舆论监督在起作用；
> * 当食品安全、房屋质量、环境污染被曝光，是记者、网民、调查者在守护社会底线。
>
> 若缺乏这些制度性的“耳目”，**信息就会失真，权力就会失控，个体将无处申冤、无从自保。**
>
> 因此，这不仅是公共议题——这关乎你：
>
> * 你能否获得真实信息
> * 你能否提出质疑
> * 你是否被听见，被理解，被保护
>
> 我们建设这个平台，正是希望唤起更多人对这些“看不见但重要”的机制的理解，并亲自参与到这场时代的对话中来。

---

## ✅ 使用建议

* 在**底部栏**使用“简洁版”文字，配上“了解更多”跳转链接
* 在“加入我们”或“项目愿景”页中完整呈现“延展版”
* 加一张视觉图（可协助制作），表达“我们每个人都与公共表达相连”的理念

---